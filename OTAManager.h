#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include "DeviceConfig.h"
#include <ArduinoJson.h>
#include <WiFiClient.h>
#include "DeviceManager.h"
// Platform-specific includes are now handled in DeviceConfig.h

// Platform-specific OTA update definitions
#ifdef IS_ESP32
#define ESPhttpUpdate Update
#define HTTP_UPDATE_OK UPDATE_OK
#define HTTP_UPDATE_FAILED UPDATE_FAILED
#define HTTP_UPDATE_NO_UPDATES UPDATE_NO_UPDATES
#else
#include <ESP8266httpUpdate.h>
// ESP8266 uses ESPhttpUpdate directly
#endif

class OTAManager
{
public:
    // Progress tracking structure for SSE
    struct ProgressInfo
    {
        String status = "idle";       // idle, checking, downloading, installing, completed, failed
        int percentage = 0;           // 0-100
        int current = 0;              // Current bytes
        int total = 0;                // Total bytes
        String message = "";          // Status message
        unsigned long lastUpdate = 0; // Timestamp of last update
    };

private:
    String _serverHost;
    int _serverPort;
    String _updateCheckEndpoint;
    String _firmwareDownloadEndpoint;
    String _deviceModel;
    String _currentVersion;

    WiFiClient _wifiClient;
    HTTPClient _httpClient;

#ifdef IS_ESP32
    HTTPUpdate httpUpdate;
#endif

    // Progress tracking for SSE
    ProgressInfo _progressInfo;

    // RGB flashing during updates
    DeviceManager *_deviceManager;
    bool _isFlashing;
    unsigned long _lastFlashTime;
    bool _flashState;
    static const unsigned long FLASH_INTERVAL = 500; // Flash every 500ms

public:
    OTAManager()
        : _serverHost(OTA_SERVER_HOST), _serverPort(OTA_SERVER_PORT),
          _updateCheckEndpoint(UPDATE_CHECK_ENDPOINT), _firmwareDownloadEndpoint(FIRMWARE_DOWNLOAD_ENDPOINT),
          _deviceModel(DEVICE_MODEL), _currentVersion(CURRENT_VERSION),
          _deviceManager(nullptr), _isFlashing(false), _lastFlashTime(0), _flashState(false)
    {
    }

    void begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" OTA Manager initialized");
        Serial.print("Device Model: ");
        Serial.println(_deviceModel);
        Serial.print("Current Version: ");
        Serial.println(_currentVersion);
        Serial.print("OTA Server: ");
        Serial.print(_serverHost);
        Serial.print(":");
        Serial.println(_serverPort);
    }

    // Set device manager for RGB control during updates
    void setDeviceManager(DeviceManager *deviceManager)
    {
        _deviceManager = deviceManager;
    }

    struct UpdateInfo
    {
        bool available;
        String version;
        String releaseNotes;
        String error;
    };

    UpdateInfo getUpdateInfo()
    {
        UpdateInfo info;
        info.available = false;

        if (WiFi.status() != WL_CONNECTED)
        {
            info.error = "WiFi not connected";
            return info;
        }

        Serial.println("=== OTA UPDATE CHECK ===");
        Serial.print("Checking for updates from: ");
        Serial.print(_serverHost);
        Serial.print(":");
        Serial.print(_serverPort);
        Serial.println(_updateCheckEndpoint);

        _httpClient.begin(_wifiClient, _serverHost, _serverPort, _updateCheckEndpoint);
        _httpClient.addHeader("Content-Type", "application/json");
        _httpClient.setTimeout(10000);

        String payload = "{\"model\":\"" + _deviceModel + "\",\"version\":\"" + _currentVersion + "\"}";
        Serial.print("Request payload: ");
        Serial.println(payload);

        int httpResponseCode = _httpClient.POST(payload);
        Serial.print("HTTP Response Code: ");
        Serial.println(httpResponseCode);

        if (httpResponseCode > 0)
        {
            String response = _httpClient.getString();
            Serial.print("Server response: ");
            Serial.println(response);

            DynamicJsonDocument doc(1024);
            DeserializationError error = deserializeJson(doc, response);

            if (error)
            {
                Serial.print("JSON parsing failed: ");
                Serial.println(error.c_str());
                info.error = "Invalid server response";
            }
            else
            {
                bool updateAvailable = doc["updateAvailable"];
                if (updateAvailable)
                {
                    info.available = true;
                    info.version = doc["version"].as<String>();
                    info.releaseNotes = doc["releaseNotes"].as<String>();
                    Serial.println("Update available!");
                    Serial.print("New version: ");
                    Serial.println(info.version);
                }
                else
                {
                    Serial.println("No updates available");
                }
            }
        }
        else
        {
            if (httpResponseCode == -1)
            {
                info.error = "Server not reachable";
                Serial.println("Server not reachable (connection failed)");
            }
            else if (httpResponseCode == -11)
            {
                info.error = "Server timeout";
                Serial.println("Server timeout (request timed out)");
            }
            else
            {
                info.error = "HTTP error: " + String(httpResponseCode);
                Serial.print("HTTP request failed with error: ");
                Serial.println(httpResponseCode);
            }
        }

        _httpClient.end();
        Serial.println("=== OTA CHECK COMPLETE ===");
        return info;
    }

    // Check if auto-update is enabled (stored in EEPROM at address 500)
    bool isAutoUpdateEnabled()
    {
        return EEPROM.read(500) == 1;
    }

    void checkForUpdates()
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            Serial.println("=== OTA ERROR ===");
            Serial.println("Cannot check for updates - WiFi not connected");
            Serial.print("WiFi Status: ");
            Serial.println(WiFi.status());
            Serial.println("================");
            updateProgress("failed", 0, 0, 0, "WiFi not connected");
            return;
        }

        updateProgress("checking", 0, 0, 0, "Checking for updates...");

        Serial.println("=== OTA Update Check Started ===");
        Serial.print("WiFi Status: Connected to ");
        Serial.println(WiFi.SSID());
        Serial.print("Local IP: ");
        Serial.println(WiFi.localIP());
        Serial.print("Free Heap: ");
        Serial.print(ESP.getFreeHeap());
        Serial.println(" bytes");

        // Prepare JSON payload
        DynamicJsonDocument requestDoc(256);
        requestDoc["model"] = _deviceModel;
        requestDoc["version"] = _currentVersion;

        String requestBody;
        serializeJson(requestDoc, requestBody);

        // Make HTTP POST request
        String url = "http://" + _serverHost + ":" + String(_serverPort) + _updateCheckEndpoint;

        Serial.println("=== OTA Request Details ===");
        Serial.println("Server: " + _serverHost + ":" + String(_serverPort));
        Serial.println("URL: " + url);
        Serial.println("Device Model: " + _deviceModel);
        Serial.println("Current Version: " + _currentVersion);
        Serial.println("Request Body: " + requestBody);

        Serial.println("Initializing HTTP client...");
        _httpClient.begin(_wifiClient, url);
        _httpClient.addHeader("Content-Type", "application/json");
        _httpClient.setTimeout(10000); // 10 second timeout

        Serial.println("Sending POST request to server...");
        unsigned long requestStart = millis();
        int httpResponseCode = _httpClient.POST(requestBody);
        unsigned long requestTime = millis() - requestStart;

        Serial.println("=== OTA Response ===");
        Serial.print("HTTP Response Code: ");
        Serial.println(httpResponseCode);
        Serial.print("Request Time: ");
        Serial.print(requestTime);
        Serial.println(" ms");

        if (httpResponseCode > 0)
        {
            String response = _httpClient.getString();
            Serial.print("Response Length: ");
            Serial.print(response.length());
            Serial.println(" bytes");
            Serial.println("Server Response: " + response);

            // Parse response
            DynamicJsonDocument responseDoc(512);
            DeserializationError error = deserializeJson(responseDoc, response);

            if (!error)
            {
                bool updateAvailable = responseDoc["updateAvailable"];
                Serial.print("Update Available: ");
                Serial.println(updateAvailable ? "YES" : "NO");

                if (updateAvailable)
                {
                    String newVersion = responseDoc["version"];
                    String releaseNotes = responseDoc["releaseNotes"];

                    Serial.println("=== UPDATE FOUND ===");
                    Serial.println("Current Version: " + _currentVersion);
                    Serial.println("New Version: " + newVersion);
                    Serial.println("Release Notes: " + releaseNotes);

                    // Check if auto-update is enabled
                    bool autoUpdateEnabled = isAutoUpdateEnabled();
                    Serial.print("Auto-update enabled: ");
                    Serial.println(autoUpdateEnabled ? "YES" : "NO");

                    if (autoUpdateEnabled)
                    {
                        Serial.println("Auto-update is enabled - performing update");
                        performOTAUpdate();
                    }
                    else
                    {
                        Serial.println("Auto-update is disabled - update available but not installing");
                    }
                    Serial.println("==================");
                }
                else
                {
                    Serial.println("=== NO UPDATES ===");
                    Serial.println("Device is up to date");
                    Serial.println("=================");
                }
            }
            else
            {
                Serial.println("=== JSON PARSE ERROR ===");
                Serial.print("Error: ");
                Serial.println(error.c_str());
                Serial.println("Raw response: " + response);
                Serial.println("=======================");
            }
        }
        else
        {
            Serial.println("=== HTTP REQUEST FAILED ===");
            Serial.print("Error Code: ");
            Serial.println(httpResponseCode);
            if (httpResponseCode == -1)
            {
                Serial.println("Connection failed - check server IP and port");
            }
            else if (httpResponseCode == -11)
            {
                Serial.println("Read timeout - server may be slow or unreachable");
            }
            Serial.println("==========================");
        }

        _httpClient.end();
        Serial.println("=== OTA Update Check Finished ===");
    }

    // RGB flashing control methods
    void startRGBFlashing()
    {
        if (_deviceManager != nullptr)
        {
            _isFlashing = true;
            _lastFlashTime = millis();
            _flashState = false;
            Serial.println("Started RGB flashing for OTA update");
        }
    }

    void stopRGBFlashing()
    {
        if (_deviceManager != nullptr && _isFlashing)
        {
            _isFlashing = false;
            Serial.println("Stopped RGB flashing for OTA update");
            // Restore normal RGB states for all switches
            restoreNormalRGBStates();
        }
    }

    void handleRGBFlashing()
    {
        if (_isFlashing && _deviceManager != nullptr)
        {
            unsigned long currentTime = millis();
            if (currentTime - _lastFlashTime >= FLASH_INTERVAL)
            {
                _lastFlashTime = currentTime;
                _flashState = !_flashState;

                // Flash all RGB lights
                flashAllRGBLights(_flashState);
            }
        }
    }

    // Force update installation (for manual updates via web interface)
    void performManualUpdate()
    {
        Serial.println("=== MANUAL UPDATE REQUESTED ===");
        Serial.println("Bypassing auto-update setting for manual installation");

        // Initialize progress tracking
        updateProgress("starting", 0, 0, 0, "Preparing update...");

        performOTAUpdate();
    }

    // Get current progress info for SSE
    ProgressInfo getProgressInfo()
    {
        return _progressInfo;
    }

    // Update progress info (called internally during updates)
    void updateProgress(const String &status, int percentage = 0, int current = 0, int total = 0, const String &message = "")
    {
        _progressInfo.status = status;
        _progressInfo.percentage = percentage;
        _progressInfo.current = current;
        _progressInfo.total = total;
        _progressInfo.message = message;
        _progressInfo.lastUpdate = millis();
    }

private:
    void performOTAUpdate()
    {
        Serial.println("Starting OTA update...");

        String firmwareUrl = "http://" + _serverHost + ":" + String(_serverPort) + _firmwareDownloadEndpoint;

        // Configure update client with platform-specific optimizations
#ifdef IS_ESP32
        // ESP32 doesn't have setLedPin method
        // LED control is handled by our RGB flashing
#else
        // ESP8266-specific configuration
        ESPhttpUpdate.setLedPin(LED_BUILTIN, LOW);
        ESPhttpUpdate.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);
#endif

        // Capture 'this' pointer for lambda callbacks
        OTAManager *self = this;

        // Setup callbacks (platform-specific)
#ifdef IS_ESP32
        httpUpdate.onStart([self]()
                           {
            Serial.println("OTA update started");
            Serial.println("This should take 30-60 seconds...");
            self->startRGBFlashing();
            self->updateProgress("downloading", 0, 0, 0, "Starting download..."); });

        httpUpdate.onEnd([self]()
                         {
            Serial.println("OTA update finished successfully!");
            self->stopRGBFlashing();
            self->updateProgress("completed", 100, 0, 0, "Update completed successfully!"); });

        httpUpdate.onProgress([self](int cur, int total)
                              {
            static unsigned long lastProgressTime = 0;
            unsigned long now = millis();

            // Update progress every 500ms for real-time SSE updates
            if (now - lastProgressTime > 500 || cur == total) {
                int percentage = (cur * 100) / total;
                Serial.printf("OTA Progress: %u%% (%d/%d bytes)\n", percentage, cur, total);

                String message = "Downloading firmware... " + String(percentage) + "%";
                if (cur == total) {
                    message = "Download complete, installing...";
                    self->updateProgress("installing", percentage, cur, total, message);
                } else {
                    self->updateProgress("downloading", percentage, cur, total, message);
                }

                lastProgressTime = now;
            } });

        httpUpdate.onError([self](int error)
                           {
            Serial.printf("OTA Error[%u]: Update failed\n", error);
            self->stopRGBFlashing();
            String errorMsg = "Update failed: Error " + String(error);
            self->updateProgress("failed", 0, 0, 0, errorMsg); });
#else
        ESPhttpUpdate.onStart([self]()
                              {
            Serial.println("OTA update started");
            Serial.println("This should take 30-60 seconds...");
            self->startRGBFlashing();
            self->updateProgress("downloading", 0, 0, 0, "Starting download..."); });

        ESPhttpUpdate.onEnd([self]()
                            {
            Serial.println("OTA update finished successfully!");
            self->stopRGBFlashing();
            self->updateProgress("completed", 100, 0, 0, "Update completed successfully!"); });

        ESPhttpUpdate.onProgress([self](int cur, int total)
                                 {
            static unsigned long lastProgressTime = 0;
            unsigned long now = millis();

            // Update progress every 500ms for real-time SSE updates
            if (now - lastProgressTime > 500 || cur == total) {
                int percentage = (cur * 100) / total;
                Serial.printf("OTA Progress: %u%% (%d/%d bytes)\n", percentage, cur, total);

                String message = "Downloading firmware... " + String(percentage) + "%";
                if (cur == total) {
                    message = "Download complete, installing...";
                    self->updateProgress("installing", percentage, cur, total, message);
                } else {
                    self->updateProgress("downloading", percentage, cur, total, message);
                }

                lastProgressTime = now;
            } });

        ESPhttpUpdate.onError([self](int error)
                              {
            Serial.printf("OTA Error[%u]: %s\n", error, ESPhttpUpdate.getLastErrorString().c_str());
            self->stopRGBFlashing();
            String errorMsg = "Update failed: " + ESPhttpUpdate.getLastErrorString();
            self->updateProgress("failed", 0, 0, 0, errorMsg); });
#endif

        // Use optimized WiFi client
        WiFiClient updateClient;

        // Perform the update (platform-specific)
        Serial.println("Downloading firmware...");
        unsigned long startTime = millis();

#ifdef IS_ESP32
        // ESP32 uses HTTPUpdate class differently
        httpUpdate.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);
        t_httpUpdate_return result = httpUpdate.update(updateClient, firmwareUrl);
#else
        // ESP8266 uses ESPhttpUpdate
        t_httpUpdate_return result = ESPhttpUpdate.update(updateClient, firmwareUrl);
#endif

        unsigned long updateTime = millis() - startTime;

        switch (result)
        {
        case HTTP_UPDATE_FAILED:
#ifdef IS_ESP32
            Serial.printf("OTA Update failed after %lu ms. Check server connectivity.\n", updateTime);
#else
            Serial.printf("OTA Update failed after %lu ms. Error (%d): %s\n",
                          updateTime, ESPhttpUpdate.getLastError(), ESPhttpUpdate.getLastErrorString().c_str());
#endif
            break;

        case HTTP_UPDATE_NO_UPDATES:
            Serial.printf("OTA: No updates available (checked in %lu ms)\n", updateTime);
            break;

        case HTTP_UPDATE_OK:
            Serial.printf("OTA Update successful in %lu ms (%.1f seconds)! Restarting...\n",
                          updateTime, updateTime / 1000.0);
            delay(1000); // Brief delay before restart
            ESP_RESTART();
            break;
        }
    }

    // Helper methods for RGB flashing
    void flashAllRGBLights(bool state)
    {
        if (_deviceManager == nullptr)
            return;

        // Use appropriate hardware manager based on device configuration
#if USE_SHIFT_REGISTERS
        auto *shiftRegister = _deviceManager->getShiftRegister();
        if (shiftRegister == nullptr)
            return;

        // Flash all RGB lights with orange color (red + green) when state is true, off when false
        for (int i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            if (state)
            {
                // Orange flash (red + green)
                shiftRegister->setRGB(i, 255, 255, 0);
            }
            else
            {
                // Turn off all RGB
                shiftRegister->setRGB(i, 0, 0, 0);
            }
        }
#else
        auto *directPinManager = _deviceManager->getDirectPinManager();
        if (directPinManager == nullptr)
            return;

        // Flash all RGB lights with orange color (red + green) when state is true, off when false
        for (int i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            if (state)
            {
                // Orange flash (red + green) - full color support for ESP32
                directPinManager->setRGB(i, 255, 255, 0);
            }
            else
            {
                // Turn off all RGB
                directPinManager->setRGB(i, 0, 0, 0);
            }
        }
#endif
    }

    void restoreNormalRGBStates()
    {
        if (_deviceManager == nullptr)
            return;

        // Restore normal RGB states for all switches based on their current state
        for (int i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            // This will restore the RGB to the appropriate color based on switch state
            _deviceManager->updateRGBForSwitch(i);
        }
    }
};

#endif // OTA_MANAGER_H
