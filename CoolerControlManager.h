#ifndef COOLER_CONTROL_MANAGER_H
#define COOLER_CONTROL_MANAGER_H

#include "DeviceConfig.h"

// Only compile this manager for cooler control devices
#if defined(DEVICE_MODEL_COOLER_CONTROL) && defined(IS_ESP32)

#include "SHT30TemperatureSensor.h"
#include "RXB22RFReceiver.h"
#include "BuzzerManager.h"

class CoolerControlManager
{
private:
    // Device-specific managers
    SHT30TemperatureSensor _temperatureSensor;
    RXB22RFReceiver _rfReceiver;
    BuzzerManager _buzzer;
    
    bool _initialized;
    
    // Temperature monitoring
    float _temperatureThreshold;
    bool _temperatureAlertActive;
    unsigned long _lastTemperatureCheck;
    static const unsigned long TEMPERATURE_CHECK_INTERVAL = 30000; // 30 seconds
    
    // RF command handling
    unsigned long _lastRFCommand;
    static const unsigned long RF_COMMAND_COOLDOWN = 500; // 500ms
    
    // Status tracking
    bool _coolerRunning;
    unsigned long _coolerStartTime;
    
    // RF command callback
    static CoolerControlManager* _instance;
    static void rfCommandReceived(unsigned long code, unsigned int bitLength)
    {
        if (_instance)
        {
            _instance->handleRFCommand(code, bitLength);
        }
    }
    
    // Handle received RF commands
    void handleRFCommand(unsigned long code, unsigned int bitLength)
    {
        unsigned long now = millis();
        
        // Prevent command flooding
        if (now - _lastRFCommand < RF_COMMAND_COOLDOWN)
        {
            return;
        }
        
        _lastRFCommand = now;
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" RF Command received: 0x");
        Serial.print(code, HEX);
        Serial.print(" (");
        Serial.print(bitLength);
        Serial.println(" bits)");
        
        // Play RF received sound
        _buzzer.playRFReceived();
        
        // Process command based on code
        processRFCommand(code);
    }
    
    // Process specific RF commands
    void processRFCommand(unsigned long code)
    {
        // Example RF command mapping (customize based on your remote)
        switch (code)
        {
            case 0x1234: // Power toggle
                Serial.println("RF Command: Power Toggle");
                _buzzer.playRFCommand(1);
                // Add power toggle logic here
                break;
                
            case 0x5678: // Temperature up
                Serial.println("RF Command: Temperature Up");
                _temperatureThreshold += 1.0;
                _buzzer.playRFCommand(2);
                break;
                
            case 0x9ABC: // Temperature down
                Serial.println("RF Command: Temperature Down");
                _temperatureThreshold -= 1.0;
                _buzzer.playRFCommand(2);
                break;
                
            case 0xDEF0: // Emergency stop
                Serial.println("RF Command: Emergency Stop");
                _buzzer.playAlertSound();
                // Add emergency stop logic here
                break;
                
            default:
                Serial.print("RF Command: Unknown (0x");
                Serial.print(code, HEX);
                Serial.println(")");
                _buzzer.playBeep();
                break;
        }
    }

public:
    CoolerControlManager() 
        : _initialized(false), _temperatureThreshold(25.0), _temperatureAlertActive(false),
          _lastTemperatureCheck(0), _lastRFCommand(0), _coolerRunning(false), _coolerStartTime(0)
    {
        _instance = this;
        Serial.print(DEVICE_TYPE);
        Serial.println(" Cooler Control Manager constructor");
    }
    
    // Initialize all cooler control components
    bool begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing Cooler Control Manager...");
        
        bool success = true;
        
        // Initialize temperature sensor
        if (!_temperatureSensor.begin())
        {
            Serial.println("ERROR: Failed to initialize temperature sensor");
            success = false;
        }
        
        // Initialize RF receiver
        if (!_rfReceiver.begin())
        {
            Serial.println("ERROR: Failed to initialize RF receiver");
            success = false;
        }
        else
        {
            _rfReceiver.setReceiveCallback(rfCommandReceived);
        }
        
        // Initialize buzzer
        if (!_buzzer.begin())
        {
            Serial.println("ERROR: Failed to initialize buzzer");
            success = false;
        }
        
        if (success)
        {
            _initialized = true;
            Serial.print(DEVICE_TYPE);
            Serial.println(" Cooler Control Manager initialized successfully");
            
            // Play startup sound
            _buzzer.playStartupSound();
        }
        else
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" Cooler Control Manager initialization failed");
            _buzzer.playErrorSound();
        }
        
        return success;
    }
    
    // Update method to be called in main loop
    void update()
    {
        if (!_initialized) return;
        
        // Update buzzer
        _buzzer.update();
        
        // Check temperature periodically
        unsigned long now = millis();
        if (now - _lastTemperatureCheck >= TEMPERATURE_CHECK_INTERVAL)
        {
            checkTemperature();
            _lastTemperatureCheck = now;
        }
    }
    
    // Check temperature and handle alerts
    void checkTemperature()
    {
        float temperature = _temperatureSensor.getTemperature();
        float humidity = _temperatureSensor.getHumidity();
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" Temperature: ");
        Serial.print(temperature, 1);
        Serial.print("°C, Humidity: ");
        Serial.print(humidity, 1);
        Serial.println("%");
        
        // Check for temperature threshold
        if (temperature > _temperatureThreshold)
        {
            if (!_temperatureAlertActive)
            {
                Serial.print(DEVICE_TYPE);
                Serial.println(" Temperature threshold exceeded!");
                _buzzer.playTemperatureAlert();
                _temperatureAlertActive = true;
            }
        }
        else
        {
            if (_temperatureAlertActive)
            {
                Serial.print(DEVICE_TYPE);
                Serial.println(" Temperature back to normal");
                _buzzer.playSuccessSound();
                _temperatureAlertActive = false;
            }
        }
    }
    
    // Get temperature reading
    float getTemperature()
    {
        return _temperatureSensor.getTemperature();
    }
    
    // Get humidity reading
    float getHumidity()
    {
        return _temperatureSensor.getHumidity();
    }
    
    // Get temperature in Fahrenheit
    float getTemperatureFahrenheit()
    {
        return _temperatureSensor.getTemperatureFahrenheit();
    }
    
    // Get formatted sensor reading
    String getTemperatureString()
    {
        return _temperatureSensor.getReadingString();
    }
    
    // Set temperature threshold
    void setTemperatureThreshold(float threshold)
    {
        _temperatureThreshold = threshold;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Temperature threshold set to ");
        Serial.print(threshold, 1);
        Serial.println("°C");
        _buzzer.playBeep();
    }
    
    // Get temperature threshold
    float getTemperatureThreshold()
    {
        return _temperatureThreshold;
    }
    
    // Manual RF test
    void testRFReceiver(unsigned long testCode)
    {
        _rfReceiver.simulateReceive(testCode, 24);
    }
    
    // Get RF receiver status
    String getRFStatus()
    {
        return _rfReceiver.getStatus();
    }
    
    // Get buzzer status
    String getBuzzerStatus()
    {
        return _buzzer.getStatus();
    }
    
    // Test all components
    void testAllComponents()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Testing all cooler control components...");
        
        // Test temperature sensor
        Serial.println("Testing temperature sensor...");
        Serial.println(_temperatureSensor.getReadingString());
        
        // Test RF receiver
        Serial.println("Testing RF receiver...");
        testRFReceiver(0x1234);
        
        // Test buzzer
        Serial.println("Testing buzzer...");
        _buzzer.testAllSounds();
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" Component test complete");
    }
    
    // Get overall status
    String getStatus()
    {
        if (!_initialized)
        {
            return "Not initialized";
        }
        
        String status = "Temp: " + _temperatureSensor.getReadingString();
        status += ", Threshold: " + String(_temperatureThreshold, 1) + "°C";
        status += ", Alert: " + String(_temperatureAlertActive ? "Active" : "Normal");
        status += ", RF: " + String(_rfReceiver.getSignalStrength()) + "%";
        
        return status;
    }
    
    // Cleanup
    ~CoolerControlManager()
    {
        _instance = nullptr;
    }
};

// Static instance pointer
CoolerControlManager* CoolerControlManager::_instance = nullptr;

#endif // DEVICE_MODEL_COOLER_CONTROL && IS_ESP32

#endif // COOLER_CONTROL_MANAGER_H
